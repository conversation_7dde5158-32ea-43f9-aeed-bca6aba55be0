<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Airtable Browser Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; margin-bottom: 30px; }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .results {
            margin-top: 30px;
            padding: 20px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .field-mapping {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .field-mapping h4 { margin-top: 0; }
        .field-mapping code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Airtable Browser Test</h1>
        <p>This tool tests your Airtable configuration using the exact same field mappings and data structure as your production code.</p>
        
        <div class="test-section">
            <h3>🔧 Configuration</h3>
            <div class="form-group">
                <label for="airtableKey">Airtable API Key / Personal Access Token:</label>
                <input type="password" id="airtableKey" placeholder="pat... or key...">
                <small>Your API key (starts with 'pat' for Personal Access Token or 'key' for legacy API key)</small>
            </div>
            
            <div class="form-group">
                <label for="airtableBase">Airtable Base ID:</label>
                <input type="text" id="airtableBase" placeholder="appXXXXXXXXXXXXXX">
                <small>Your base ID (starts with 'app' and is 17 characters long)</small>
            </div>
            
            <div class="form-group">
                <label for="tableName">Table Name:</label>
                <input type="text" id="tableName" value="Prospects">
                <small>The name of your table (default: "Prospects")</small>
            </div>
        </div>

        <div class="field-mapping">
            <h4>📋 Field Mapping (Production Configuration)</h4>
            <p>Your Airtable table should have these exact field names:</p>
            <ul>
                <li><code>Nom</code> - Text field for name</li>
                <li><code>Entreprise</code> - Text field for company</li>
                <li><code>Email</code> - Email field</li>
                <li><code>Téléphone</code> - Text field for phone</li>
                <li><code>Statut</code> - Single select field for status</li>
                <li><code>Contexte</code> - Long text field for context</li>
                <li><code>utm_source</code> - Text field for UTM source</li>
                <li><code>utm_campaign</code> - Text field for UTM campaign</li>
                <li><code>Date</code> - Date/time field (auto-populated)</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 Test Data</h3>
            <div class="form-group">
                <label for="testName">Name:</label>
                <input type="text" id="testName" value="Test User Browser">
            </div>
            
            <div class="form-group">
                <label for="testCompany">Company:</label>
                <input type="text" id="testCompany" value="Test Company Browser">
            </div>
            
            <div class="form-group">
                <label for="testEmail">Email:</label>
                <input type="email" id="testEmail" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="testPhone">Phone:</label>
                <input type="text" id="testPhone" value="+33123456789">
            </div>
            
            <div class="form-group">
                <label for="testStatus">Status:</label>
                <select id="testStatus">
                    <option value="reprise">Reprise récente</option>
                    <option value="optimisation" selected>Optimisation</option>
                    <option value="autre">Autre</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="testContext">Context:</label>
                <textarea id="testContext" rows="3">Test browser de la configuration Airtable</textarea>
            </div>
            
            <div class="form-group">
                <label for="testUtmSource">UTM Source:</label>
                <input type="text" id="testUtmSource" value="test-browser">
            </div>
            
            <div class="form-group">
                <label for="testUtmCampaign">UTM Campaign:</label>
                <input type="text" id="testUtmCampaign" value="debug-browser">
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Tests</h3>
            <button onclick="validateConfig()">1. Validate Configuration</button>
            <button onclick="testConnection()">2. Test Connection</button>
            <button onclick="testTableAccess()">3. Test Table Access</button>
            <button onclick="createTestRecord()">4. Create Test Record</button>
            <button onclick="runAllTests()">🏃 Run All Tests</button>
        </div>

        <div id="results" class="results info" style="display: none;">
            Ready to run tests...
        </div>
    </div>

    <script>
        let currentConfig = {};

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.className = `results ${type}`;
            resultsDiv.textContent += message + '\n';
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent = '';
            resultsDiv.style.display = 'block';
        }

        function getConfig() {
            return {
                AIRTABLE_KEY: document.getElementById('airtableKey').value.trim(),
                AIRTABLE_BASE: document.getElementById('airtableBase').value.trim(),
                TABLE_NAME: document.getElementById('tableName').value.trim() || 'Prospects',
                TEST_DATA: {
                    name: document.getElementById('testName').value.trim(),
                    company: document.getElementById('testCompany').value.trim(),
                    email: document.getElementById('testEmail').value.trim(),
                    phone: document.getElementById('testPhone').value.trim(),
                    status: document.getElementById('testStatus').value,
                    context: document.getElementById('testContext').value.trim(),
                    utm_source: document.getElementById('testUtmSource').value.trim(),
                    utm_campaign: document.getElementById('testUtmCampaign').value.trim()
                }
            };
        }

        function validateConfig() {
            clearResults();
            log('🔧 Configuration Validation', 'info');
            log('=' .repeat(40));
            
            currentConfig = getConfig();
            const issues = [];
            
            if (!currentConfig.AIRTABLE_KEY) {
                issues.push('AIRTABLE_KEY is missing');
            } else {
                log(`✅ AIRTABLE_KEY found (length: ${currentConfig.AIRTABLE_KEY.length})`);
                
                if (currentConfig.AIRTABLE_KEY.startsWith('pat')) {
                    log('✅ AIRTABLE_KEY uses correct Personal Access Token format');
                } else if (currentConfig.AIRTABLE_KEY.startsWith('key')) {
                    log('⚠️  AIRTABLE_KEY uses deprecated API key format', 'warning');
                } else {
                    log('⚠️  AIRTABLE_KEY format not recognized', 'warning');
                }
            }
            
            if (!currentConfig.AIRTABLE_BASE) {
                issues.push('AIRTABLE_BASE is missing');
            } else {
                log(`✅ AIRTABLE_BASE found (${currentConfig.AIRTABLE_BASE})`);
                
                if (currentConfig.AIRTABLE_BASE.startsWith('app') && currentConfig.AIRTABLE_BASE.length === 17) {
                    log('✅ AIRTABLE_BASE format looks correct');
                } else {
                    log('⚠️  AIRTABLE_BASE format might be incorrect', 'warning');
                }
            }
            
            if (issues.length > 0) {
                log('\n❌ Configuration issues found:', 'error');
                issues.forEach(issue => log(`  - ${issue}`, 'error'));
                return false;
            }
            
            log('\n✅ Configuration validation passed!');
            return true;
        }

        async function testConnection() {
            if (!validateConfig()) return false;
            
            log('\n🔗 Airtable Connection Test');
            log('=' .repeat(40));
            
            try {
                const url = `https://api.airtable.com/v0/${currentConfig.AIRTABLE_BASE}`;
                log(`Testing connection to: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentConfig.AIRTABLE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    log('✅ Successfully connected to Airtable base');
                    return true;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    log(`❌ Connection failed with status ${response.status}`, 'error');
                    log(`Error details: ${JSON.stringify(errorData, null, 2)}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Connection error: ${error.message}`, 'error');
                return false;
            }
        }

        async function testTableAccess() {
            if (!currentConfig.AIRTABLE_KEY) {
                log('❌ Please validate configuration first', 'error');
                return false;
            }
            
            log('\n📋 Table Access Test');
            log('=' .repeat(40));
            
            try {
                const url = `https://api.airtable.com/v0/${currentConfig.AIRTABLE_BASE}/${encodeURIComponent(currentConfig.TABLE_NAME)}?maxRecords=1`;
                log(`Testing access to table: ${currentConfig.TABLE_NAME}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentConfig.AIRTABLE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Successfully accessed table "${currentConfig.TABLE_NAME}"`);
                    log(`Table contains ${data.records ? data.records.length : 0} records (showing max 1)`);
                    
                    if (data.records && data.records.length > 0) {
                        const sampleRecord = data.records[0];
                        log('Sample record fields:');
                        Object.keys(sampleRecord.fields || {}).forEach(field => {
                            log(`  - ${field}`);
                        });
                    }
                    
                    return true;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    log(`❌ Table access failed with status ${response.status}`, 'error');
                    log(`Error details: ${JSON.stringify(errorData, null, 2)}`, 'error');
                    
                    if (response.status === 404) {
                        log(`⚠️  Table "${currentConfig.TABLE_NAME}" might not exist or you don't have access to it`, 'warning');
                    }
                    
                    return false;
                }
            } catch (error) {
                log(`❌ Table access error: ${error.message}`, 'error');
                return false;
            }
        }

        async function createTestRecord() {
            if (!currentConfig.AIRTABLE_KEY) {
                log('❌ Please validate configuration first', 'error');
                return false;
            }
            
            log('\n📝 Test Record Creation');
            log('=' .repeat(40));
            
            // Use the EXACT same field mapping as the Netlify function
            const fields = {
                "Nom": currentConfig.TEST_DATA.name || '',
                "Entreprise": currentConfig.TEST_DATA.company || '',
                "Email": currentConfig.TEST_DATA.email || '',
                "Téléphone": currentConfig.TEST_DATA.phone || '',
                "Statut": currentConfig.TEST_DATA.status || '',
                "Contexte": currentConfig.TEST_DATA.context || '',
                "utm_source": currentConfig.TEST_DATA.utm_source || '',
                "utm_campaign": currentConfig.TEST_DATA.utm_campaign || '',
                "Date": new Date().toISOString()
            };
            
            log('Creating test record with fields:');
            Object.entries(fields).forEach(([key, value]) => {
                log(`  ${key}: "${value}"`);
            });
            
            try {
                const url = `https://api.airtable.com/v0/${currentConfig.AIRTABLE_BASE}/${encodeURIComponent(currentConfig.TABLE_NAME)}`;
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentConfig.AIRTABLE_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ fields })
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    log(`✅ Test record created successfully!`);
                    log(`Record ID: ${responseData.id}`);
                    log('Record details:');
                    log(JSON.stringify(responseData, null, 2));
                    return true;
                } else {
                    log(`❌ Record creation failed with status ${response.status}`, 'error');
                    log('Response details:', 'error');
                    log(JSON.stringify(responseData, null, 2), 'error');
                    
                    if (responseData.error && responseData.error.type === 'INVALID_PERMISSIONS_OR_MODEL_NOT_FOUND') {
                        log('\n⚠️  This error suggests one of the following issues:', 'warning');
                        log('  1. The table "Prospects" does not exist in your base', 'warning');
                        log('  2. Your API key does not have permission to write to this table', 'warning');
                        log('  3. One or more field names do not match your Airtable schema', 'warning');
                        log('  4. Your base ID is incorrect', 'warning');
                    }
                    
                    return false;
                }
            } catch (error) {
                log(`❌ Record creation error: ${error.message}`, 'error');
                return false;
            }
        }

        async function runAllTests() {
            clearResults();
            log('🧪 Running All Airtable Tests');
            log('=' .repeat(50));
            
            const results = {
                config: false,
                connection: false,
                tableAccess: false,
                recordCreation: false
            };
            
            // Step 1: Validate configuration
            results.config = validateConfig();
            if (!results.config) {
                log('\n❌ Configuration validation failed. Please fix the issues above.', 'error');
                return;
            }
            
            // Step 2: Test connection
            results.connection = await testConnection();
            if (!results.connection) {
                log('\n❌ Connection test failed. Check your AIRTABLE_KEY and AIRTABLE_BASE.', 'error');
                return;
            }
            
            // Step 3: Test table access
            results.tableAccess = await testTableAccess();
            if (!results.tableAccess) {
                log('\n❌ Table access test failed. Check your table name and permissions.', 'error');
                return;
            }
            
            // Step 4: Create test record
            results.recordCreation = await createTestRecord();
            
            // Summary
            log('\n📊 Test Summary');
            log('=' .repeat(40));
            Object.entries(results).forEach(([test, passed]) => {
                if (passed) {
                    log(`✅ ${test}: PASSED`);
                } else {
                    log(`❌ ${test}: FAILED`);
                }
            });
            
            if (Object.values(results).every(result => result)) {
                log('\n🎉 All tests passed! Your Airtable configuration is working correctly.');
                log('Your production endpoints should now work with these credentials.');
            } else {
                log('\n💥 Some tests failed. Please address the issues above.', 'error');
            }
        }
    </script>
</body>
</html>
